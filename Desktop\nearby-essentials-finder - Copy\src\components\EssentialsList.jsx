"use client"

import { useState, useEffect, useRef } from "react"
import { MapPin, Clock, Phone, Star } from "lucide-react"
import { Badge } from "@/components/ui/badge"

function EssentialItem({ essential, isVisible }) {
  const getServiceIcon = (type) => {
    switch (type) {
      case "hospital":
        return "🏥"
      case "atm":
        return "🏧"
      case "grocery":
        return "🛒"
      case "pharmacy":
        return "💊"
      case "gas_station":
        return "⛽"
      default:
        return "📍"
    }
  }

  const getServiceColor = (type) => {
    switch (type) {
      case "hospital":
        return "bg-red-100 text-red-800"
      case "atm":
        return "bg-green-100 text-green-800"
      case "grocery":
        return "bg-amber-100 text-amber-800"
      case "pharmacy":
        return "bg-purple-100 text-purple-800"
      case "gas_station":
        return "bg-cyan-100 text-cyan-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (!isVisible) {
    return (
      <div className="h-32 flex items-center justify-center border-b">
        <div className="animate-pulse flex space-x-4 w-full p-4">
          <div className="rounded-full bg-gray-200 h-12 w-12"></div>
          <div className="flex-1 space-y-2 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/4"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 border-b last:border-b-0 hover:bg-gray-50 transition-colors">
      <div className="flex items-start gap-3">
        <div className="text-2xl flex-shrink-0 mt-1">{getServiceIcon(essential.type)}</div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2 mb-2">
            <h3 className="font-semibold text-gray-900 truncate">{essential.name}</h3>
            <Badge className={getServiceColor(essential.type)}>{essential.type.replace("_", " ")}</Badge>
          </div>

          <div className="space-y-1 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <MapPin className="w-3 h-3 flex-shrink-0" />
              <span className="truncate">{essential.address}</span>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <span className="font-medium text-blue-600">{essential.distance.toFixed(1)} km</span>
              </div>

              {essential.rating && (
                <div className="flex items-center gap-1">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span>{essential.rating}</span>
                </div>
              )}

              {essential.isOpen !== undefined && (
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span className={essential.isOpen ? "text-green-600" : "text-red-600"}>
                    {essential.isOpen ? "Open" : "Closed"}
                  </span>
                </div>
              )}
            </div>

            {essential.phone && (
              <div className="flex items-center gap-1">
                <Phone className="w-3 h-3" />
                <a href={`tel:${essential.phone}`} className="text-blue-600 hover:underline">
                  {essential.phone}
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function EssentialsList({ essentials }) {
  const [visibleItems, setVisibleItems] = useState(new Set())
  const observerRef = useRef(null)
  const itemRefs = useRef([])

  useEffect(() => {
    // Initialize Intersection Observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = Number.parseInt(entry.target.getAttribute("data-index") || "0")
          if (entry.isIntersecting) {
            setVisibleItems((prev) => new Set([...prev, index]))
          }
        })
      },
      {
        rootMargin: "50px 0px",
        threshold: 0.1,
      },
    )

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  useEffect(() => {
    // Observe all items
    itemRefs.current.forEach((ref, index) => {
      if (ref && observerRef.current) {
        ref.setAttribute("data-index", index.toString())
        observerRef.current.observe(ref)
      }
    })

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [essentials])

  return (
    <div className="max-h-96 overflow-y-auto">
      {essentials.map((essential, index) => (
        <div
          key={essential.id}
          ref={(el) => {
            itemRefs.current[index] = el
          }}
        >
          <EssentialItem essential={essential} isVisible={visibleItems.has(index)} />
        </div>
      ))}

      {essentials.length === 0 && (
        <div className="p-8 text-center text-gray-500">
          <MapPin className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>No essentials found nearby</p>
        </div>
      )}
    </div>
  )
}
