"use client"

import { useEffect, useRef } from "react"

export default function MapCanvas({ userLocation, essentials }) {
  const canvasRef = useRef(null)

  const getServiceColor = (type) => {
    switch (type) {
      case "hospital":
        return "#ef4444" // red
      case "atm":
        return "#10b981" // green
      case "grocery":
        return "#f59e0b" // amber
      case "pharmacy":
        return "#8b5cf6" // purple
      case "gas_station":
        return "#06b6d4" // cyan
      default:
        return "#6b7280" // gray
    }
  }

  const drawMap = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const { width, height } = canvas

    // Clear canvas
    ctx.clearRect(0, 0, width, height)

    // Draw background grid
    ctx.strokeStyle = "#e5e7eb"
    ctx.lineWidth = 1

    // Vertical lines
    for (let x = 0; x <= width; x += 40) {
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, height)
      ctx.stroke()
    }

    // Horizontal lines
    for (let y = 0; y <= height; y += 40) {
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(width, y)
      ctx.stroke()
    }

    // Calculate bounds for positioning
    const allLats = [userLocation.latitude, ...essentials.map((e) => e.latitude)]
    const allLngs = [userLocation.longitude, ...essentials.map((e) => e.longitude)]

    const minLat = Math.min(...allLats)
    const maxLat = Math.max(...allLats)
    const minLng = Math.min(...allLngs)
    const maxLng = Math.max(...allLngs)

    const latRange = maxLat - minLat || 0.01
    const lngRange = maxLng - minLng || 0.01

    // Add padding
    const padding = 40
    const mapWidth = width - 2 * padding
    const mapHeight = height - 2 * padding

    const latToY = (lat) => padding + ((maxLat - lat) / latRange) * mapHeight
    const lngToX = (lng) => padding + ((lng - minLng) / lngRange) * mapWidth

    // Draw essentials markers
    essentials.forEach((essential) => {
      const x = lngToX(essential.longitude)
      const y = latToY(essential.latitude)

      // Draw marker circle
      ctx.fillStyle = getServiceColor(essential.type)
      ctx.beginPath()
      ctx.arc(x, y, 8, 0, 2 * Math.PI)
      ctx.fill()

      // Draw marker border
      ctx.strokeStyle = "#ffffff"
      ctx.lineWidth = 2
      ctx.stroke()

      // Draw distance label
      ctx.fillStyle = "#374151"
      ctx.font = "10px sans-serif"
      ctx.textAlign = "center"
      ctx.fillText(`${essential.distance.toFixed(1)}km`, x, y + 20)
    })

    // Draw user location (on top)
    const userX = lngToX(userLocation.longitude)
    const userY = latToY(userLocation.latitude)

    // User location outer circle (pulse effect)
    ctx.fillStyle = "rgba(59, 130, 246, 0.3)"
    ctx.beginPath()
    ctx.arc(userX, userY, 16, 0, 2 * Math.PI)
    ctx.fill()

    // User location inner circle
    ctx.fillStyle = "#3b82f6"
    ctx.beginPath()
    ctx.arc(userX, userY, 10, 0, 2 * Math.PI)
    ctx.fill()

    // User location border
    ctx.strokeStyle = "#ffffff"
    ctx.lineWidth = 3
    ctx.stroke()

    // User label
    ctx.fillStyle = "#1f2937"
    ctx.font = "bold 12px sans-serif"
    ctx.textAlign = "center"
    ctx.fillText("You", userX, userY - 25)
  }

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    // Set canvas size
    const updateCanvasSize = () => {
      const container = canvas.parentElement
      if (container) {
        const rect = container.getBoundingClientRect()
        canvas.width = rect.width
        canvas.height = Math.min(400, rect.width * 0.75) // Maintain aspect ratio
        drawMap()
      }
    }

    updateCanvasSize()
    window.addEventListener("resize", updateCanvasSize)

    return () => window.removeEventListener("resize", updateCanvasSize)
  }, [userLocation, essentials])

  return (
    <div className="w-full">
      <canvas ref={canvasRef} className="w-full border rounded-lg bg-gray-50" style={{ maxHeight: "400px" }} />

      {/* Legend */}
      <div className="mt-4 flex flex-wrap gap-3 text-sm">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
          <span>You</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <span>Hospital</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span>ATM</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-amber-500"></div>
          <span>Grocery</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-purple-500"></div>
          <span>Pharmacy</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-cyan-500"></div>
          <span>Gas Station</span>
        </div>
      </div>
    </div>
  )
}
