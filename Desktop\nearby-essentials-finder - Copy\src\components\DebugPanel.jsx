"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function DebugPanel({ essentials, userLocation }) {
  const [showDebug, setShowDebug] = useState(false)

  if (!showDebug) {
    return (
      <Button variant="outline" size="sm" onClick={() => setShowDebug(true)} className="fixed bottom-4 right-4 z-50">
        Debug Info
      </Button>
    )
  }

  const typeCounts = essentials.reduce((acc, essential) => {
    acc[essential.type] = (acc[essential.type] || 0) + 1
    return acc
  }, {})

  return (
    <Card className="fixed bottom-4 right-4 w-80 max-h-96 overflow-y-auto z-50">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex justify-between items-center">
          Debug Info
          <Button variant="ghost" size="sm" onClick={() => setShowDebug(false)}>
            ×
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="text-xs space-y-2">
        <div>
          <strong>User Location:</strong>
          <br />
          {userLocation?.latitude.toFixed(4)}, {userLocation?.longitude.toFixed(4)}
        </div>

        <div>
          <strong>Total Places:</strong> {essentials.length}
        </div>

        <div>
          <strong>By Type:</strong>
          <ul className="ml-2">
            {Object.entries(typeCounts).map(([type, count]) => (
              <li key={type}>
                {type}: {count}
              </li>
            ))}
          </ul>
        </div>

        <div>
          <strong>First 5 Places:</strong>
          <ul className="ml-2">
            {essentials.slice(0, 5).map((place) => (
              <li key={place.id} className="truncate">
                {place.name} ({place.type}) - {place.distance.toFixed(1)}km
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
