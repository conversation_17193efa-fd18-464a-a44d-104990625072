export const mockEssentials = [
  // Hospitals
  {
    id: "1",
    name: "City General Hospital",
    type: "hospital",
    address: "123 Medical Center Dr, Downtown",
    latitude: 40.7589,
    longitude: -73.9851,
    distance: 0,
    phone: "+****************",
    rating: 4.2,
    isOpen: true,
  },
  {
    id: "2",
    name: "St. Mary's Emergency Care",
    type: "hospital",
    address: "456 Health Ave, Midtown",
    latitude: 40.7614,
    longitude: -73.9776,
    distance: 0,
    phone: "+****************",
    rating: 4.5,
    isOpen: true,
  },
  {
    id: "3",
    name: "Metro Urgent Care",
    type: "hospital",
    address: "789 Care Blvd, Uptown",
    latitude: 40.7505,
    longitude: -73.9934,
    distance: 0,
    phone: "+****************",
    rating: 3.8,
    isOpen: false,
  },

  // ATMs
  {
    id: "4",
    name: "Chase Bank ATM",
    type: "atm",
    address: "321 Finance St, Business District",
    latitude: 40.758,
    longitude: -73.9855,
    distance: 0,
    isOpen: true,
  },
  {
    id: "5",
    name: "Bank of America ATM",
    type: "atm",
    address: "654 Money Ave, Financial Center",
    latitude: 40.762,
    longitude: -73.978,
    distance: 0,
    isOpen: true,
  },
  {
    id: "6",
    name: "Wells Fargo ATM",
    type: "atm",
    address: "987 Banking Blvd, Commerce Plaza",
    latitude: 40.751,
    longitude: -73.994,
    distance: 0,
    isOpen: true,
  },
  {
    id: "7",
    name: "CitiBank ATM",
    type: "atm",
    address: "147 Cash Corner, Market Square",
    latitude: 40.7595,
    longitude: -73.9845,
    distance: 0,
    isOpen: true,
  },

  // Grocery Stores
  {
    id: "8",
    name: "Fresh Market Grocery",
    type: "grocery",
    address: "258 Food St, Residential Area",
    latitude: 40.7575,
    longitude: -73.986,
    distance: 0,
    phone: "+****************",
    rating: 4.3,
    isOpen: true,
  },
  {
    id: "9",
    name: "SuperMart Express",
    type: "grocery",
    address: "369 Shopping Ave, Suburb Center",
    latitude: 40.7625,
    longitude: -73.9775,
    distance: 0,
    phone: "+****************",
    rating: 4.0,
    isOpen: true,
  },
  {
    id: "10",
    name: "Organic Foods Co-op",
    type: "grocery",
    address: "741 Green Way, Eco District",
    latitude: 40.7515,
    longitude: -73.9935,
    distance: 0,
    phone: "+****************",
    rating: 4.7,
    isOpen: false,
  },
  {
    id: "11",
    name: "Corner Convenience Store",
    type: "grocery",
    address: "852 Quick Stop Ln, Neighborhood",
    latitude: 40.76,
    longitude: -73.984,
    distance: 0,
    phone: "+****************",
    rating: 3.5,
    isOpen: true,
  },

  // Pharmacies
  {
    id: "12",
    name: "CVS Pharmacy",
    type: "pharmacy",
    address: "159 Medicine Dr, Health Plaza",
    latitude: 40.7585,
    longitude: -73.985,
    distance: 0,
    phone: "+****************",
    rating: 4.1,
    isOpen: true,
  },
  {
    id: "13",
    name: "Walgreens",
    type: "pharmacy",
    address: "753 Prescription Ave, Wellness Center",
    latitude: 40.7615,
    longitude: -73.9785,
    distance: 0,
    phone: "+****************",
    rating: 3.9,
    isOpen: true,
  },
  {
    id: "14",
    name: "Local Family Pharmacy",
    type: "pharmacy",
    address: "486 Community St, Village Square",
    latitude: 40.752,
    longitude: -73.993,
    distance: 0,
    phone: "+****************",
    rating: 4.6,
    isOpen: false,
  },

  // Gas Stations
  {
    id: "15",
    name: "Shell Gas Station",
    type: "gas_station",
    address: "357 Fuel Rd, Highway Junction",
    latitude: 40.757,
    longitude: -73.9865,
    distance: 0,
    phone: "+****************",
    rating: 3.7,
    isOpen: true,
  },
  {
    id: "16",
    name: "Exxon Mobile",
    type: "gas_station",
    address: "951 Energy Blvd, Auto District",
    latitude: 40.763,
    longitude: -73.977,
    distance: 0,
    phone: "+****************",
    rating: 3.8,
    isOpen: true,
  },
  {
    id: "17",
    name: "BP Gas & Go",
    type: "gas_station",
    address: "624 Petrol Pike, Service Area",
    latitude: 40.7525,
    longitude: -73.9925,
    distance: 0,
    phone: "+****************",
    rating: 4.0,
    isOpen: true,
  },

  // Additional essentials for better testing
  {
    id: "18",
    name: "Metro Emergency Hospital",
    type: "hospital",
    address: "888 Emergency Ln, Critical Care",
    latitude: 40.754,
    longitude: -73.99,
    distance: 0,
    phone: "+****************",
    rating: 4.4,
    isOpen: true,
  },
  {
    id: "19",
    name: "Quick Cash ATM",
    type: "atm",
    address: "777 Fast Money St, Express Plaza",
    latitude: 40.756,
    longitude: -73.988,
    distance: 0,
    isOpen: true,
  },
  {
    id: "20",
    name: "Neighborhood Grocery",
    type: "grocery",
    address: "555 Local Ave, Community Center",
    latitude: 40.7605,
    longitude: -73.9835,
    distance: 0,
    phone: "+****************",
    rating: 4.2,
    isOpen: true,
  },
]
