"use client"

import { useState, useEffect } from "react"
import { MapPin, Navigation, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import MapCanvas from "./components/MapCanvas"
import EssentialsList from "./components/EssentialsList"
import { fetchNearbyPlacesOSM, fetchNearbyPlaces } from "./data/mockData"
import DebugPanel from "./components/DebugPanel"

export default function App() {
  const [userLocation, setUserLocation] = useState(null)
  const [essentials, setEssentials] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const getUserLocation = async () => {
    setLoading(true)
    setError(null)

    if (!navigator.geolocation) {
      setError("Geolocation is not supported by this browser.")
      setLoading(false)
      return
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
        }
        setUserLocation(location)

        try {
          // Try OpenStreetMap first (free), fallback to Google Places if needed
          let nearbyEssentials = await fetchNearbyPlacesOSM(location.latitude, location.longitude, 5000)

          // If OSM doesn't return enough results, try Google Places
          if (nearbyEssentials.length < 10) {
            console.log("Trying Google Places API as fallback...")
            const googlePlaces = await fetchNearbyPlaces(location.latitude, location.longitude, 5000)
            nearbyEssentials = [...nearbyEssentials, ...googlePlaces]
          }

          // Remove duplicates and sort by distance
          const uniquePlaces = nearbyEssentials.filter(
            (place, index, self) =>
              index === self.findIndex((p) => p.name === place.name && Math.abs(p.latitude - place.latitude) < 0.001),
          )

          setEssentials(uniquePlaces.sort((a, b) => a.distance - b.distance))
        } catch (error) {
          console.error("Error fetching places:", error)
          setError("Failed to fetch nearby places. Please try again.")
        }

        setLoading(false)
      },
      (error) => {
        let errorMessage = "Unable to retrieve your location."
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Location access denied. Please enable location services."
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable."
            break
          case error.TIMEOUT:
            errorMessage = "Location request timed out."
            break
        }
        setError(errorMessage)
        setLoading(false)
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 300000,
      },
    )
  }

  useEffect(() => {
    // Auto-request location on component mount
    getUserLocation()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
            <MapPin className="w-8 h-8 text-blue-600" />
            Nearby Essentials Finder
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Find essential services like hospitals, ATMs, and grocery stores near your location
          </p>
        </div>

        {/* Location Status */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Navigation className="w-5 h-5" />
              Location Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading && (
              <div className="flex items-center gap-2 text-blue-600">
                <Loader2 className="w-4 h-4 animate-spin" />
                Getting your location...
              </div>
            )}

            {error && <div className="text-red-600 mb-3">{error}</div>}

            {userLocation && (
              <div className="text-green-600 mb-3">
                ✓ Location found: {userLocation.latitude.toFixed(4)}, {userLocation.longitude.toFixed(4)}
                <span className="text-sm text-gray-500 ml-2">(±{Math.round(userLocation.accuracy)}m accuracy)</span>
              </div>
            )}

            <Button
              onClick={getUserLocation}
              disabled={loading}
              variant={userLocation ? "outline" : "default"}
              className="w-full sm:w-auto"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Getting Location...
                </>
              ) : (
                <>
                  <Navigation className="w-4 h-4 mr-2" />
                  {userLocation ? "Refresh Location" : "Get My Location"}
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Main Content */}
        {userLocation && essentials.length > 0 && (
          <div className="grid lg:grid-cols-2 gap-6">
            {/* Map Canvas */}
            <Card className="lg:sticky lg:top-4 h-fit">
              <CardHeader>
                <CardTitle>Map View</CardTitle>
              </CardHeader>
              <CardContent>
                <MapCanvas
                  userLocation={userLocation}
                  essentials={essentials.slice(0, 20)} // Limit for performance
                />
              </CardContent>
            </Card>

            {/* Essentials List */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Nearby Essentials ({essentials.length})</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <EssentialsList essentials={essentials} />
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!userLocation && !loading && (
          <Card className="text-center py-12">
            <CardContent>
              <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">Location Required</h3>
              <p className="text-gray-500 mb-4">Please allow location access to find nearby essential services</p>
              <Button onClick={getUserLocation}>
                <Navigation className="w-4 h-4 mr-2" />
                Enable Location
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Debug Panel */}
        {userLocation && <DebugPanel essentials={essentials} userLocation={userLocation} />}
      </div>
    </div>
  )
}
