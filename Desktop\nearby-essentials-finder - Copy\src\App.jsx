import { useState, useEffect } from "react"
import { MapPin, Navigation, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import MapCanvas from "./components/MapCanvas"
import EssentialsList from "./components/EssentialsList"
import { fetchNearbyPlacesOSM, fetchNearbyPlaces } from "./data/mockData"
import DebugPanel from "./components/DebugPanel"
import { getAddressFromCoordinates, formatCoordinates } from "./lib/geocoding"

export default function App() {
  const [userLocation, setUserLocation] = useState(null)
  const [userAddress, setUserAddress] = useState(null)
  const [addressLoading, setAddressLoading] = useState(false)
  const [essentials, setEssentials] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const getUserLocation = async () => {
    setLoading(true)
    setError(null)

    if (!navigator.geolocation) {
      setError("Geolocation is not supported by this browser.")
      setLoading(false)
      return
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
        }
        setUserLocation(location)

        // Get address from coordinates
        setAddressLoading(true)
        try {
          const address = await getAddressFromCoordinates(location.latitude, location.longitude)
          setUserAddress(address)
        } catch (error) {
          console.error("Error getting address:", error)
          setUserAddress(formatCoordinates(location.latitude, location.longitude, location.accuracy))
        }
        setAddressLoading(false)

        try {
          // Try OpenStreetMap first (free), fallback to Google Places if needed
          let nearbyEssentials = await fetchNearbyPlacesOSM(location.latitude, location.longitude, 5000)

          // If OSM doesn't return enough results, try Google Places
          if (nearbyEssentials.length < 10) {
            console.log("Trying Google Places API as fallback...")
            const googlePlaces = await fetchNearbyPlaces(location.latitude, location.longitude, 5000)
            nearbyEssentials = [...nearbyEssentials, ...googlePlaces]
          }

          // Remove duplicates and sort by distance
          const uniquePlaces = nearbyEssentials.filter(
            (place, index, self) =>
              index === self.findIndex((p) => p.name === place.name && Math.abs(p.latitude - place.latitude) < 0.001),
          )

          const sortedPlaces = uniquePlaces.sort((a, b) => a.distance - b.distance)
          console.log("Final essentials data for map:", sortedPlaces)

          // If no real data, add some test data for debugging
          if (sortedPlaces.length === 0) {
            console.log("No real data found, adding test markers for debugging")
            const testData = [
              {
                id: "test_1",
                name: "Test Grocery Store",
                type: "grocery",
                latitude: location.latitude + 0.001,
                longitude: location.longitude + 0.001,
                distance: 0.1,
                address: "Test Address"
              },
              {
                id: "test_2",
                name: "Test Pharmacy",
                type: "pharmacy",
                latitude: location.latitude - 0.001,
                longitude: location.longitude + 0.001,
                distance: 0.15,
                address: "Test Address"
              },
              {
                id: "test_3",
                name: "Test Gas Station",
                type: "gas_station",
                latitude: location.latitude + 0.001,
                longitude: location.longitude - 0.001,
                distance: 0.2,
                address: "Test Address"
              }
            ]
            setEssentials(testData)
          } else {
            setEssentials(sortedPlaces)
          }
        } catch (error) {
          console.error("Error fetching places:", error)
          setError("Failed to fetch nearby places. Please try again.")
        }

        setLoading(false)
      },
      (error) => {
        let errorMessage = "Unable to retrieve your location."
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Location access denied. Please enable location services."
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable."
            break
          case error.TIMEOUT:
            errorMessage = "Location request timed out."
            break
        }
        setError(errorMessage)
        setLoading(false)
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 300000,
      },
    )
  }

  useEffect(() => {
    // Auto-request location on component mount
    getUserLocation()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50" >
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200  top-0 z-10 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3 flex items-center justify-center gap-3">
              <MapPin className="w-10 h-10 text-blue-600" />
              Nearby Essentials Finder
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Discover hospitals, ATMs, groceries, pharmacies, and gas stations in your area with our smart location finder
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8" >

        {/* Location Status */}
        <Card className="mb-8 shadow-lg border-0 bg-white/70 backdrop-blur-sm">
          <CardHeader className="pb-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-t-lg">
            <CardTitle className="text-xl flex items-center gap-3">
              <Navigation className="w-6 h-6" />
              Location Status
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            {loading && (
              <div className="flex items-center gap-3 text-blue-600 bg-blue-50 p-4 rounded-lg border border-blue-200">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span className="font-medium">Getting your location...</span>
              </div>
            )}

            {error && (
              <div className="text-red-600 bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                <div className="font-medium">❌ {error}</div>
              </div>
            )}

            {userLocation && (
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 text-green-700 font-medium mb-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  Location found
                </div>
                {addressLoading ? (
                  <div className="flex items-center gap-2 text-blue-600">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Getting address...</span>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {userAddress && (
                      <div className="text-gray-800 font-medium">{userAddress}</div>
                    )}
                    <div className="text-xs text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded">
                      {formatCoordinates(userLocation.latitude, userLocation.longitude, userLocation.accuracy)}
                    </div>
                  </div>
                )}
              </div>
            )}

            <Button
              onClick={getUserLocation}
              disabled={loading}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 border-0"
            >
              {loading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Getting Location...
                </>
              ) : (
                <>
                  <Navigation className="w-5 h-5 mr-2" />
                  {userLocation ? "🔄 Refresh Location" : "📍 Get My Location"}
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Main Content */}
        {userLocation && essentials.length > 0 && (
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Map Canvas */}
            <Card className="lg:sticky lg:top-24 h-fit shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <MapPin className="w-6 h-6" />
                  Interactive Map View
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <MapCanvas
                  userLocation={userLocation}
                  essentials={essentials.slice(0, 20)} // Limit for performance
                />
              </CardContent>
            </Card>

            {/* Essentials List */}
            <div className="space-y-6" >
              <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-t-lg">
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center text-sm font-bold">
                      {essentials.length}
                    </div>
                    Nearby Essentials Found
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <EssentialsList essentials={essentials} />
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!userLocation && !loading && (
          <Card className="text-center py-16 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardContent>
              <div className="bg-gradient-to-br from-blue-100 to-purple-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6">
                <MapPin className="w-12 h-12 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-3">Location Access Required</h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto leading-relaxed">
                To discover amazing places near you, we need access to your location. Your privacy is protected - we only use this to find nearby services.
              </p>
              <Button
                onClick={getUserLocation}
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                <Navigation className="w-5 h-5 mr-2" />
                🌟 Enable Location Access
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Debug Panel */}
        {userLocation && <DebugPanel essentials={essentials} userLocation={userLocation} />}
      </div>

      {/* Footer */}
      <footer className="bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center">
            <p className="text-gray-600 mb-2">
              Built with ❤️ using React, Vite, and OpenStreetMap
            </p>
            <p className="text-sm text-gray-500">
              Find essential services near you with real-time location data
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
